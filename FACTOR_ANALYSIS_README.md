# 因子分析功能使用说明

本文档说明如何使用 `basic_factor.py` 中新增的因子分析功能。

## 功能概述

新增的因子分析功能参考了 `stock_recommander.py` 中的 `analyze_and_plot_factor` 函数，可以：

1. 加载指定的pkl文件
2. 获取所有因子列
3. 依次对每个因子进行分析
4. 生成因子分析图表和统计信息

## 使用方法

### 1. 查看帮助信息

```bash
# 查看总体帮助
python basic_factor.py --help

# 查看因子分析功能帮助
python basic_factor.py analyze --help

# 查看主要功能帮助
python basic_factor.py main --help
```

### 2. 计算因子（主要功能）

```bash
# 基本用法：计算因子
python basic_factor.py main 20050101 20250630 basic_data.pkl

# 计算因子并同时进行分析
python basic_factor.py main 20050101 20250630 basic_data.pkl --analyze_factors
```

### 3. 分析已有的因子文件

```bash
# 分析所有因子（使用文件中的全部日期范围）
python basic_factor.py analyze basic_data.pkl

# 分析指定日期范围的因子
python basic_factor.py analyze basic_data.pkl --start_date 20230101 --end_date 20231231
```

## 输出结果

### 分析过程输出

- 数据加载信息（文件路径、数据形状、索引信息等）
- 找到的因子列数量
- 每个因子的分析进度
- 信息系数（Information Coefficient）
- 成功/失败的分析统计

### 生成的文件

- 图表文件保存在 `alphas_fig/` 目录下
- 每个因子生成一个PNG图表文件，文件名为因子名称
- 图表包含因子的收益率分析、分位数表现等信息

## 依赖库

因子分析功能需要以下库：

```bash
pip install alphalens matplotlib pillow
```

如果缺少这些库，程序会显示警告信息并跳过分析功能。

## 注意事项

1. **数据格式要求**：
   - pkl文件必须包含 'close' 列（用于计算收益率）
   - 数据应该有多级索引（日期和股票代码）

2. **排除的列**：
   - 默认排除以下列：'close', 'open', 'high', 'low', 'volume', 'amount', 'vol', 'adjclose', 'return', 'vwap', 'weight'
   - 这些列被认为是价格/成交量数据而非因子

3. **因子有效性检查**：
   - 跳过所有值都是NaN的因子
   - 跳过值的唯一性不足的因子（如常数因子）

4. **性能考虑**：
   - 分析大量因子可能需要较长时间
   - 建议在测试时先使用较小的日期范围

## 示例

```bash
# 完整的工作流程示例

# 1. 计算因子数据
cd /Users/<USER>/ai_retry/900010
python ../basic_factor.py main 20050101 20250630 basic_data.pkl

# 2. 分析所有因子
python ../basic_factor.py analyze basic_data.pkl --start_date 20230101 --end_date 20231231

# 3. 查看生成的图表
ls alphas_fig/
```

## 故障排除

1. **ImportError: alphalens相关库**
   ```bash
   pip install alphalens matplotlib pillow
   ```

2. **文件不存在错误**
   - 确认pkl文件路径正确
   - 确认当前工作目录

3. **数据格式错误**
   - 检查pkl文件是否包含必要的列
   - 确认数据索引格式正确

4. **