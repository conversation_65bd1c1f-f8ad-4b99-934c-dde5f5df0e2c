#!/usr/bin/env python3
"""
测试全None列的填充处理
"""
import pandas as pd
import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_all_none_columns():
    """测试全None列的处理"""
    print("=== 测试全None列的填充处理 ===")
    
    from basic_factor import FactorCalculator
    calculator = FactorCalculator(token="test_token")
    
    # 创建包含全None列的测试数据
    test_data = pd.DataFrame({
        'normal_col': [1.0, 2.0, 3.0, 4.0, 5.0],
        'partial_none_col': [1.0, None, 3.0, None, 5.0],
        'all_none_col': [None, None, None, None, None],
        'all_nan_col': [np.nan, np.nan, np.nan, np.nan, np.nan],
        'mixed_none_nan': [None, np.nan, None, np.nan, None]
    }, index=pd.date_range('2023-01-01', periods=5, freq='D'))
    
    print("原始数据:")
    print(test_data)
    print("\n数据类型:")
    print(test_data.dtypes)
    print("\n缺失值统计:")
    print(test_data.isnull().sum())
    print()
    
    # 测试_robust_fillna函数
    print("测试 _robust_fillna 函数...")
    filled_data = calculator._robust_fillna(test_data)
    
    print("填充后的数据:")
    print(filled_data)
    print("\n填充后缺失值统计:")
    print(filled_data.isnull().sum())
    print()
    
    # 验证填充结果
    print("=== 验证填充结果 ===")
    
    # 检查normal_col（应该保持不变）
    print(f"normal_col 是否保持不变: {filled_data['normal_col'].equals(test_data['normal_col'])}")
    
    # 检查partial_none_col（应该用中位数填充）
    original_median = test_data['partial_none_col'].median()
    print(f"partial_none_col 原始中位数: {original_median}")
    print(f"partial_none_col 填充后: {filled_data['partial_none_col'].tolist()}")
    
    # 检查全None/NaN列（应该填充为0）
    print(f"all_none_col 填充后: {filled_data['all_none_col'].tolist()}")
    print(f"all_nan_col 填充后: {filled_data['all_nan_col'].tolist()}")
    print(f"mixed_none_nan 填充后: {filled_data['mixed_none_nan'].tolist()}")
    
    # 验证是否所有值都被填充
    has_missing = filled_data.isnull().any().any()
    print(f"\n填充后是否还有缺失值: {has_missing}")
    
    if not has_missing:
        print("✓ 所有缺失值都已成功填充")
    else:
        print("✗ 仍有缺失值未被填充")
        print("未填充的列:")
        print(filled_data.isnull().sum()[filled_data.isnull().sum() > 0])

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    from basic_factor import FactorCalculator
    calculator = FactorCalculator(token="test_token")
    
    # 测试空DataFrame
    empty_df = pd.DataFrame()
    filled_empty = calculator._robust_fillna(empty_df)
    print(f"空DataFrame处理结果: {filled_empty.shape}")
    
    # 测试只有一行的DataFrame
    single_row = pd.DataFrame({
        'col1': [None],
        'col2': [1.0],
        'col3': [np.nan]
    })
    filled_single = calculator._robust_fillna(single_row)
    print(f"单行DataFrame处理结果:")
    print(filled_single)
    
    # 测试只有NaN和None的DataFrame
    all_missing = pd.DataFrame({
        'col1': [None, None, None],
        'col2': [np.nan, np.nan, np.nan],
        'col3': [None, np.nan, None]
    })
    filled_all_missing = calculator._robust_fillna(all_missing)
    print(f"全缺失DataFrame处理结果:")
    print(filled_all_missing)
    
    print("✓ 边界情况测试完成")

if __name__ == "__main__":
    print("开始测试全None列的填充处理...")
    print()
    
    # 设置环境变量
    os.environ['TUSHARE'] = 'test_token'
    
    test_all_none_columns()
    test_edge_cases()
    
    print("\n测试完成！")
