#!/usr/bin/env python3
"""
测试移除锁机制后的代码
"""
import pandas as pd
import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能（无锁机制） ===")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'pe_ttm': [10.5, 12.3, 15.2, 8.9, 20.1],
        'pb': [1.2, 1.5, 2.1, 0.8, 3.2],
        'ps_ttm': [2.5, 3.0, 3.1, 1.8, 4.5],
        'dv_ttm': [0.05, 0.03, 0.02, 0.08, 0.01],
        'total_mv': [1000, 1500, 2000, 800, 2500],
        'circ_mv': [800, 1200, 1800, 600, 2000]
    }, index=pd.date_range('2023-01-01', periods=5, freq='D'))
    
    print("测试数据:")
    print(test_data)
    print()
    
    from basic_factor import FactorCalculator
    
    try:
        # 创建计算器实例
        calculator = FactorCalculator(token="test_token")
        print("✓ FactorCalculator 创建成功")
        
        # 测试各种因子计算
        print("测试 calculate_size_factors...")
        size_factors = calculator.calculate_size_factors(test_data)
        print(f"Size factors shape: {size_factors.shape}")
        print(size_factors.head())
        print()
        
        print("测试 calculate_value_factors...")
        value_factors = calculator.calculate_value_factors(test_data)
        print(f"Value factors shape: {value_factors.shape}")
        print(value_factors.head())
        print()
        
        print("测试 calculate_advanced_value_factors...")
        advanced_factors = calculator.calculate_advanced_value_factors(test_data, pd.DataFrame())
        print(f"Advanced value factors shape: {advanced_factors.shape}")
        print(advanced_factors.head())
        print()
        
        print("测试 calculate_liquidity_factors...")
        liquidity_factors = calculator.calculate_liquidity_factors(test_data, advanced_factors)
        print(f"Liquidity factors shape: {liquidity_factors.shape}")
        print(liquidity_factors.head())
        print()
        
        # 测试缓存功能
        print("测试缓存功能...")
        cache_info = calculator.get_cache_info()
        print(f"缓存信息: {cache_info}")
        print()
        
        print("✓ 所有基本功能测试通过")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_cache_operations():
    """测试缓存操作"""
    print("=== 测试缓存操作（无锁机制） ===")
    
    from basic_factor import FactorCalculator
    
    try:
        calculator = FactorCalculator(token="test_token")
        
        # 测试添加缓存
        test_df = pd.DataFrame({
            'close': [10, 11, 12, 13, 14]
        }, index=pd.date_range('2023-01-01', periods=5, freq='D'))
        
        calculator._add_to_cache('daily', '000001.SZ', '20230101', '20230105', test_df)
        print("✓ 添加缓存成功")
        
        # 测试获取缓存
        cached_data, start, end = calculator._get_cached_data('daily', '000001.SZ', '20230101', '20230105')
        print(f"✓ 获取缓存成功，数据形状: {cached_data.shape}")
        
        # 测试清空缓存
        calculator.clear_cache()
        print("✓ 清空缓存成功")
        
        print("✓ 所有缓存操作测试通过")
        
    except Exception as e:
        print(f"✗ 缓存操作测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始测试移除锁机制后的代码...")
    print()
    
    # 设置环境变量
    os.environ['TUSHARE'] = 'test_token'
    
    test_basic_functionality()
    print()
    test_cache_operations()
    
    print("测试完成！")
