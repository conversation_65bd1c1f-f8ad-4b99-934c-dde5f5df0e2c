#!/usr/bin/env python3
"""
测试None值修复
"""
import pandas as pd
import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_none_values():
    """测试None值处理"""
    print("=== 测试None值处理 ===")
    
    # 创建包含None值的测试数据
    test_data = pd.DataFrame({
        'pe_ttm': [10.5, None, 15.2, 0, np.nan],
        'pb': [1.2, None, 2.1, 0, np.nan],
        'ps_ttm': [2.5, None, 3.1, 0, np.nan],
        'dv_ttm': [0.05, None, -0.01, 0, np.nan],
        'total_mv': [1000, None, 2000, 0, np.nan],
        'circ_mv': [800, None, 1800, 0, np.nan]
    })
    
    print("原始数据:")
    print(test_data)
    print("数据类型:")
    print(test_data.dtypes)
    print()
    
    from basic_factor import FactorCalculator
    calculator = FactorCalculator(token="test_token")
    
    try:
        # 测试size factors
        print("测试 calculate_size_factors...")
        size_factors = calculator.calculate_size_factors(test_data)
        print(f"Size factors shape: {size_factors.shape}")
        print(size_factors)
        print()
        
        # 测试advanced value factors
        print("测试 calculate_advanced_value_factors...")
        advanced_factors = calculator.calculate_advanced_value_factors(test_data, pd.DataFrame())
        print(f"Advanced value factors shape: {advanced_factors.shape}")
        print(advanced_factors)
        print()
        
        # 测试liquidity factors
        print("测试 calculate_liquidity_factors...")
        liquidity_factors = calculator.calculate_liquidity_factors(test_data, advanced_factors)
        print(f"Liquidity factors shape: {liquidity_factors.shape}")
        print(liquidity_factors)
        print()
        
        print("✓ 所有None值处理测试通过")
        
    except Exception as e:
        print(f"✗ None值处理测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始测试None值修复...")
    print()
    
    # 设置环境变量
    os.environ['TUSHARE'] = 'test_token'
    
    test_none_values()
    
    print("测试完成！")
